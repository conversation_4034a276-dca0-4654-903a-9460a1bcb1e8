plugins {
    id 'java'
    id 'application'
    id 'org.javamodularity.moduleplugin' version '1.8.12'
    id 'org.openjfx.javafxplugin' version '0.0.13'
    id 'org.beryx.jlink' version '2.25.0'
    id 'com.github.johnrengelman.shadow' version '8.1.1'
}

group 'com.logictrue'
version '1.0'

repositories {
    maven { url "https://maven.aliyun.com/repository/public" }
    // 阿里云 Gradle 插件仓库
    maven { url "https://maven.aliyun.com/repository/gradle-plugin" }
    mavenCentral()
}

ext {
    junitVersion = '5.10.0'
}

sourceCompatibility = '21'
targetCompatibility = '21'

tasks.withType(JavaCompile) {
    options.encoding = 'UTF-8'
}


application {
    mainModule = 'com.logictrue'
    mainClass = 'com.logictrue.App'
}

javafx {
    version = '21'
    modules = ['javafx.controls', 'javafx.fxml']
}

dependencies {
    implementation("com.fasterxml.jackson.core:jackson-databind:2.19.2")
    implementation("org.slf4j:slf4j-api:2.0.17")
    implementation("ch.qos.logback:logback-classic:1.5.18")
    testImplementation("org.junit.jupiter:junit-jupiter-api:${junitVersion}")
    testRuntimeOnly("org.junit.jupiter:junit-jupiter-engine:${junitVersion}")
}

test {
    useJUnitPlatform()
}


//jar {
//    String someString = ''
//    configurations.runtime.each {someString = someString + " lib//"+it.name}
//    manifest {
//        attributes 'Main-Class': 'com.it.messtart.MesStartApplication'
//        attributes 'Class-Path': someString
//    }
//
//}

//jar {
//    manifest {
//        attributes(
//                'Main-Class': 'com.it.messtart.MesStartApplication'
//        )
//    }
//    from {
//        configurations.runtimeClasspath.collect { it.isDirectory() ? it : zipTree(it) }
//    }
//    exclude 'META-INF/*.RSA', 'META-INF/*.SF', 'META-INF/*.DSA'
//}

//tasks.named('compileJava') {
//    options.compilerArgs += '--module-path'
//    options.compilerArgs += files(classpath).asType(List)
//    options.compilerArgs += '--add-modules'
//    options.compilerArgs += 'javafx.controls,javafx.fxml'
//}

jlink {
    imageZip = project.file("${layout.buildDirectory.get()}/distributions/app-${javafx.platform.classifier}.zip")
    options = ['--strip-debug', '--compress=2', '--no-header-files', '--no-man-pages']
    launcher {
        name = 'app'
    }
    jpackage {
        imageName = 'IotClient'
        installerName = 'IotClient'

        // 根据操作系统选择不同的配置
        if (org.gradle.internal.os.OperatingSystem.current().isWindows()) {
            installerType = 'exe'
            installerOptions = [
                '--win-shortcut',
                '--win-menu',
                '--win-dir-chooser',
                '--win-per-user-install',
                '--win-upgrade-uuid', 'e55e80e0-597e-4a0b-ae77-e06117109143'
            ]
        } else if (org.gradle.internal.os.OperatingSystem.current().isLinux()) {
            // 构建特定类型的包
            // ./gradlew jpackage -PinstallerType=deb
            // ./gradlew jpackage -PinstallerType=rpm
            // ./gradlew jpackage -PinstallerType=app-image
            installerType = 'app-image' // 或者 'rpm' 或 'deb'
            installerOptions = [
                '--linux-package-name', 'iot-client',
                '--linux-deb-maintainer', '<EMAIL>',
                '--linux-menu-group', 'Development',
                '--linux-app-category', 'Development',
                '--linux-shortcut'
            ]
        } else if (org.gradle.internal.os.OperatingSystem.current().isMacOsX()) {
            installerType = 'dmg'
            installerOptions = [
                '--mac-package-name', 'IotClient',
                '--mac-package-identifier', 'com.logictrue.iotclient'
            ]
        }
    }
}

jlinkZip {
    group = 'distribution'
}

shadowJar {
    archiveBaseName.set('IotClient')
    archiveClassifier.set('')
    archiveVersion.set('')
    manifest {
        attributes 'Main-Class': 'com.logictrue.App'
    }
}
